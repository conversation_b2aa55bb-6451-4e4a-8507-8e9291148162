import Link from 'next/link'
import { useRouter } from 'next/router'
import styles from '../styles/Navbar.module.css'

export default function Navbar() {
  const router = useRouter()

  return (
    <nav className={styles.navbar}>
      <div className={styles.container}>
        <Link href="/" className={styles.logo}>
          Next.js 模板
        </Link>
        
        <ul className={styles.navLinks}>
          <li>
            <Link 
              href="/" 
              className={router.pathname === '/' ? styles.active : ''}
            >
              首页
            </Link>
          </li>
          <li>
            <Link 
              href="/about" 
              className={router.pathname === '/about' ? styles.active : ''}
            >
              关于
            </Link>
          </li>
        </ul>
      </div>
    </nav>
  )
}
