/* OCR 应用样式 */
.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
  color: #333;
}

.description {
  font-size: 1.1rem;
  color: #666;
  text-align: center;
  margin-bottom: 2rem;
}

/* 错误提示 */
.error {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #fcc;
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 500;
}

/* 成功提示 */
.success {
  background-color: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 500;
}

/* 文件上传区域 */
.uploadArea {
  width: 100%;
  max-width: 600px;
  height: 300px;
  border: 2px dashed #ccc;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.uploadArea:hover {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.fileInput {
  display: none;
}

.uploadContent {
  text-align: center;
  color: #666;
}

.uploadIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.uploadContent h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: #333;
}

.uploadContent p {
  margin: 0;
  font-size: 0.9rem;
  color: #999;
}

/* 上传中状态 */
.uploading {
  text-align: center;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 预览区域 */
.previewSection {
  width: 100%;
  max-width: 900px;
  margin-top: 2rem;
}

.imageContainer {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.previewImage {
  max-width: 800px;
  max-height: 600px;
  width: auto;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 文字选择框 */
.textBox {
  position: absolute;
  background-color: rgba(0, 123, 255, 0.1);
  border: 1px solid rgba(0, 123, 255, 0.3);
  cursor: text;
  font-size: 0;
  transition: all 0.2s ease;
}

.textBox:hover {
  background-color: rgba(0, 123, 255, 0.2);
  border-color: rgba(0, 123, 255, 0.5);
}

/* 控制按钮 */
.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.ocrButton, .resetButton, .copyButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ocrButton {
  background-color: #007bff;
  color: white;
}

.ocrButton:hover:not(:disabled) {
  background-color: #0056b3;
}

.ocrButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.resetButton {
  background-color: #6c757d;
  color: white;
}

.resetButton:hover {
  background-color: #545b62;
}

.copyButton {
  background-color: #28a745;
  color: white;
}

.copyButton:hover {
  background-color: #1e7e34;
}

/* 进度条 */
.progressContainer {
  text-align: center;
  margin-bottom: 2rem;
}

.progressBar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progressFill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

/* 识别结果 */
.resultSection {
  width: 100%;
  max-width: 800px;
  margin-top: 2rem;
}

.resultSection h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.resultContainer {
  position: relative;
}

.resultText {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  background-color: #f8f9fa;
}

.resultText:focus {
  outline: none;
  border-color: #007bff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .uploadArea {
    height: 200px;
  }

  .previewImage {
    max-width: 100%;
  }

  .controls {
    flex-direction: column;
    align-items: center;
  }

  .ocrButton, .resetButton, .copyButton {
    width: 200px;
  }
}
