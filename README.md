# 图片文字识别应用 (OCR App)

这是一个基于 Next.js 和 Tesseract.js 构建的图片文字识别应用，支持从图片中提取文字内容。

## ✨ 功能特性

- 📁 **文件上传**: 支持拖拽上传和点击选择文件
- 🖼️ **图片预览**: 上传后实时预览图片
- 🔍 **OCR识别**: 使用 Tesseract.js 进行光学字符识别
- 🎯 **文字选择**: 在图片上叠加可选择的文字区域
- 📋 **一键复制**: 支持复制选中文字或全部识别结果
- 🌐 **多语言支持**: 支持中文和英文识别
- 📱 **响应式设计**: 适配各种设备屏幕

## 🚀 技术栈

- **框架**: Next.js 15
- **UI 库**: React 19
- **OCR 引擎**: Tesseract.js
- **样式**: CSS Modules
- **语言**: JavaScript

## 📋 支持的文件格式

- JPG / JPEG
- PNG
- WebP
- 最大文件大小: 10MB

## 🛠️ 开始使用

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 📖 使用说明

1. **上传图片**:
   - 拖拽图片文件到上传区域
   - 或点击上传区域选择文件

2. **开始识别**:
   - 图片上传成功后，点击"开始识别文字"按钮
   - 等待 OCR 处理完成（会显示进度条）

3. **查看结果**:
   - 识别完成后，可以在图片上看到可选择的文字区域
   - 下方会显示完整的识别结果文本

4. **复制文字**:
   - 点击图片上的文字区域复制单个词语
   - 点击"复制全部文字"按钮复制完整结果

## 🏗️ 项目结构

```
├── components/          # React 组件
│   ├── Layout.js       # 布局组件
│   ├── Navbar.js       # 导航栏组件
│   └── Footer.js       # 页脚组件
├── pages/              # 页面文件
│   ├── _app.js         # App 组件
│   ├── _document.js    # Document 组件
│   └── index.js        # 主页 (OCR 应用)
├── styles/             # 样式文件
│   ├── globals.css     # 全局样式
│   └── Home.module.css # 主页样式
├── public/             # 静态资源
└── next.config.js      # Next.js 配置
```

## ⚙️ 配置说明

### OCR 配置

应用使用 Tesseract.js 进行文字识别，支持以下配置：

- **语言包**: 中文简体 + 英文 (`chi_sim+eng`)
- **置信度过滤**: 过滤置信度低于 30% 的识别结果
- **字符白名单**: 支持数字、英文字母、常用中文字符

### Next.js 配置

`next.config.js` 中包含了支持 Tesseract.js WebAssembly 的配置。

## 🔧 自定义开发

### 添加新语言支持

修改 `pages/index.js` 中的语言配置：

```javascript
const worker = await createWorker('chi_sim+eng+fra', 1, {
  // 添加法语支持
})
```

### 调整识别参数

修改 OCR 参数以提高识别准确性：

```javascript
await worker.setParameters({
  tessedit_char_whitelist: '自定义字符集',
  tessedit_pageseg_mode: '页面分割模式',
})
```

## 🚀 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 [Vercel](https://vercel.com) 上导入项目
3. 自动部署完成

### 其他平台

确保部署平台支持 WebAssembly 和静态文件服务。

## 📝 注意事项

- 首次使用时需要下载 Tesseract.js 语言包，可能需要一些时间
- 识别准确性取决于图片质量和文字清晰度
- 建议使用高分辨率、对比度高的图片以获得最佳识别效果

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
