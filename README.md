# 图片文字识别应用 (OCR App)

这是一个基于 Next.js 和 Tesseract.js 构建的图片文字识别应用，支持从图片中提取文字内容。

## ✨ 功能特性

- 📁 **文件上传**: 支持拖拽上传和点击选择文件
- 🖼️ **图片预览**: 上传后实时预览图片
- 🔍 **智能OCR识别**: 使用优化的 Tesseract.js 进行光学字符识别
- 🎯 **精确文字选择**: 在图片上叠加可选择的文字区域，显示置信度
- 📋 **一键复制**: 支持复制选中文字或全部识别结果
- 🌐 **多语言支持**: 支持中文和英文识别
- 📱 **响应式设计**: 适配各种设备屏幕
- 🔧 **图片预处理**: 自动优化图片质量以提高识别准确性
- 📊 **质量分析**: 实时分析图片质量并提供优化建议
- ⚙️ **参数调节**: 可调节置信度阈值和页面分割模式

## 🚀 技术栈

- **框架**: Next.js 15
- **UI 库**: React 19
- **OCR 引擎**: Tesseract.js
- **样式**: CSS Modules
- **语言**: JavaScript

## 📋 支持的文件格式

- JPG / JPEG
- PNG
- WebP
- 最大文件大小: 10MB

## 🛠️ 开始使用

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 📖 使用说明

1. **上传图片**:
   - 拖拽图片文件到上传区域
   - 或点击上传区域选择文件

2. **查看图片质量分析**:
   - 上传后自动分析图片质量（对比度、清晰度、分辨率）
   - 根据分析结果获得优化建议

3. **调整识别设置**:
   - 设置置信度阈值（30%-80%）
   - 选择页面分割模式（自动检测、单列文本等）

4. **开始识别**:
   - 点击"开始识别文字"按钮
   - 系统自动进行图片预处理（灰度化、对比度增强、去噪、锐化）
   - 等待 OCR 处理完成（会显示详细进度）

5. **查看结果**:
   - 识别完成后，在图片上看到带置信度的可选择文字区域
   - 悬停查看每个词的置信度百分比
   - 下方显示完整的识别结果文本

6. **复制文字**:
   - 点击图片上的文字区域复制单个词语
   - 点击"复制全部文字"按钮复制完整结果

## 🏗️ 项目结构

```
├── components/          # React 组件
│   ├── Layout.js       # 布局组件
│   ├── Navbar.js       # 导航栏组件
│   └── Footer.js       # 页脚组件
├── pages/              # 页面文件
│   ├── _app.js         # App 组件
│   ├── _document.js    # Document 组件
│   └── index.js        # 主页 (OCR 应用)
├── styles/             # 样式文件
│   ├── globals.css     # 全局样式
│   └── Home.module.css # 主页样式
├── public/             # 静态资源
└── next.config.js      # Next.js 配置
```

## ⚙️ 配置说明

### OCR 优化配置

应用使用优化的 Tesseract.js 进行文字识别，包含以下改进：

#### 图片预处理
- **灰度化**: 转换为灰度图像以提高识别准确性
- **对比度增强**: 使用 S 曲线增强图片对比度
- **去噪处理**: 应用中值滤波去除图片噪声
- **锐化处理**: 使用卷积核增强图片清晰度
- **尺寸优化**: 自动调整图片到最适合 OCR 的分辨率

#### OCR 引擎参数
- **语言包**: 中文简体 + 英文 (`chi_sim+eng`)
- **OCR 引擎**: LSTM OCR 引擎模式
- **页面分割**: 支持多种分割模式（自动、单列、单块等）
- **置信度过滤**: 可调节的置信度阈值（30%-80%）
- **字符白名单**: 优化的中英文字符集

#### 质量检测
- **对比度分析**: 检测图片对比度是否足够
- **清晰度评估**: 基于边缘检测评估图片清晰度
- **分辨率检查**: 确保图片分辨率适合 OCR 识别
- **智能建议**: 根据分析结果提供优化建议

### Next.js 配置

`next.config.js` 中包含了支持 Tesseract.js WebAssembly 的配置。

## 🔧 自定义开发

### 添加新语言支持

修改 `pages/index.js` 中的语言配置：

```javascript
const worker = await createWorker('chi_sim+eng+fra', 1, {
  // 添加法语支持
})
```

### 调整识别参数

修改 OCR 参数以提高识别准确性：

```javascript
await worker.setParameters({
  tessedit_char_whitelist: '自定义字符集',
  tessedit_pageseg_mode: '页面分割模式',
})
```

## 🚀 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 [Vercel](https://vercel.com) 上导入项目
3. 自动部署完成

### 其他平台

确保部署平台支持 WebAssembly 和静态文件服务。

## 📝 注意事项

- 首次使用时需要下载 Tesseract.js 语言包，可能需要一些时间
- 识别准确性取决于图片质量和文字清晰度
- 建议使用高分辨率、对比度高的图片以获得最佳识别效果

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
