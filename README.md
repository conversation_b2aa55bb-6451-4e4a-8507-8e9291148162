# Next.js 项目模板

这是一个使用 [Next.js](https://nextjs.org/) 构建的现代化 Web 应用程序模板。

## 特性

- ⚡️ **Next.js 15** - 最新版本的 Next.js
- ⚛️ **React 19** - 最新版本的 React
- 💅 **CSS Modules** - 模块化 CSS 支持
- 📱 **响应式设计** - 移动端友好
- 🎨 **现代化 UI** - 简洁美观的界面设计

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看结果。

### 构建生产版本

```bash
npm run build
```

### 启动生产服务器

```bash
npm start
```

## 项目结构

```
├── components/          # React 组件
│   ├── Layout.js       # 布局组件
│   ├── Navbar.js       # 导航栏组件
│   └── Footer.js       # 页脚组件
├── pages/              # 页面文件
│   ├── api/            # API 路由
│   ├── _app.js         # App 组件
│   ├── _document.js    # Document 组件
│   ├── index.js        # 首页
│   └── about.js        # 关于页面
├── public/             # 静态资源
├── styles/             # 样式文件
│   ├── globals.css     # 全局样式
│   └── *.module.css    # 模块化样式
├── next.config.js      # Next.js 配置
├── package.json        # 项目配置
└── README.md          # 项目说明
```

## 技术栈

- **框架**: Next.js 15
- **UI 库**: React 19
- **样式**: CSS Modules
- **包管理**: npm

## 部署

### Vercel (推荐)

最简单的部署方式是使用 [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme)。

### 其他平台

你也可以部署到其他支持 Node.js 的平台，如：

- Netlify
- Heroku
- AWS
- 阿里云
- 腾讯云

## 学习资源

- [Next.js 文档](https://nextjs.org/docs) - 学习 Next.js 功能和 API
- [Learn Next.js](https://nextjs.org/learn) - 交互式 Next.js 教程
- [Next.js GitHub 仓库](https://github.com/vercel/next.js/) - 欢迎反馈和贡献！

## 许可证

MIT License
