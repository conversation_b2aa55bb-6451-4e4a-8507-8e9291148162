/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // 图片优化配置
  images: {
    domains: [],
    formats: ['image/webp', 'image/avif'],
  },

  // 支持 Tesseract.js 的 WebAssembly
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
      }
    }

    // 支持 .wasm 文件
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    }

    return config
  },

  // 环境变量配置
  env: {
    CUSTOM_KEY: 'my-value',
  },

  // 重定向配置示例
  async redirects() {
    return [
      // {
      //   source: '/old-page',
      //   destination: '/new-page',
      //   permanent: true,
      // },
    ]
  },

  // 重写配置示例
  async rewrites() {
    return [
      // {
      //   source: '/api/:path*',
      //   destination: 'https://api.example.com/:path*',
      // },
    ]
  },
}

module.exports = nextConfig
