import Head from 'next/head'
import Layout from '../components/Layout'
import styles from '../styles/About.module.css'

export default function About() {
  return (
    <Layout>
      <Head>
        <title>关于我们 - Next.js 项目模板</title>
        <meta name="description" content="关于我们的页面" />
      </Head>

      <main className={styles.main}>
        <h1 className={styles.title}>关于我们</h1>
        
        <div className={styles.content}>
          <p>
            这是一个使用 Next.js 构建的现代化 Web 应用程序模板。
          </p>
          
          <h2>特性</h2>
          <ul>
            <li>🚀 基于 Next.js 15</li>
            <li>⚛️ React 19</li>
            <li>💅 CSS Modules</li>
            <li>📱 响应式设计</li>
            <li>🔧 ESLint 配置</li>
            <li>🎨 现代化 UI</li>
          </ul>

          <h2>技术栈</h2>
          <div className={styles.techStack}>
            <span className={styles.tech}>Next.js</span>
            <span className={styles.tech}>React</span>
            <span className={styles.tech}>CSS Modules</span>
            <span className={styles.tech}>ESLint</span>
          </div>
        </div>
      </main>
    </Layout>
  )
}
