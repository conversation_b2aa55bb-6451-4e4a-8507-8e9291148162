import Head from 'next/head'
import Layout from '../components/Layout'
import styles from '../styles/Home.module.css'

export default function Home() {
  return (
    <Layout>
      <Head>
        <title>已添加谷歌代码</title>
        <meta name="description" content="已成功添加 Google Analytics 跟踪代码" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className={styles.main}>
        <h1 className={styles.title}>
          已添加谷歌代码
        </h1>

        <p className={styles.description}>
          开始编辑{' '}
          <code className={styles.code}>pages/index.js</code>
        </p>

        <div className={styles.grid}>
          <a href="https://nextjs.org/docs" className={styles.card}>
            <h2>文档 &rarr;</h2>
            <p>查找有关 Next.js 功能和 API 的深入信息。</p>
          </a>

          <a href="https://nextjs.org/learn" className={styles.card}>
            <h2>学习 &rarr;</h2>
            <p>通过交互式课程学习 Next.js！</p>
          </a>

          <a
            href="https://github.com/vercel/next.js/tree/canary/examples"
            className={styles.card}
          >
            <h2>示例 &rarr;</h2>
            <p>发现并部署样板 Next.js 项目。</p>
          </a>

          <a
            href="https://vercel.com/new?utm_source=create-next-app&utm_medium=default-template&utm_campaign=create-next-app"
            className={styles.card}
          >
            <h2>部署 &rarr;</h2>
            <p>立即将您的 Next.js 站点部署到公共 URL。</p>
          </a>
        </div>
      </main>
    </Layout>
  )
}
