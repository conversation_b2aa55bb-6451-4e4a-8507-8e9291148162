import Head from 'next/head'
import Layout from '../components/Layout'
import { useState, useRef, useCallback } from 'react'
import { createWorker } from 'tesseract.js'
import styles from '../styles/Home.module.css'

export default function Home() {
  const [selectedFile, setSelectedFile] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [ocrResult, setOcrResult] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState('')
  const [progress, setProgress] = useState(0)
  const [textBoxes, setTextBoxes] = useState([])
  const [copySuccess, setCopySuccess] = useState('')
  const fileInputRef = useRef(null)
  const imageRef = useRef(null)

  // 支持的文件类型
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  const maxFileSize = 10 * 1024 * 1024 // 10MB

  // 验证文件
  const validateFile = (file) => {
    if (!supportedTypes.includes(file.type)) {
      return '不支持的文件格式。请选择 JPG、PNG 或 WebP 格式的图片。'
    }
    if (file.size > maxFileSize) {
      return '文件过大。请选择小于 10MB 的图片。'
    }
    return null
  }

  // 处理文件选择
  const handleFileSelect = useCallback((file) => {
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    setError('')
    setSelectedFile(file)
    setIsUploading(true)

    // 创建图片预览
    const reader = new FileReader()
    reader.onload = (e) => {
      setImagePreview(e.target.result)
      setIsUploading(false)
    }
    reader.onerror = () => {
      setError('文件读取失败，请重试。')
      setIsUploading(false)
    }
    reader.readAsDataURL(file)
  }, [])

  // 处理拖拽上传
  const handleDrop = useCallback((e) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  const handleDragOver = useCallback((e) => {
    e.preventDefault()
  }, [])

  // 处理点击上传
  const handleFileInputChange = useCallback((e) => {
    const files = Array.from(e.target.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  // OCR 文字识别
  const performOCR = async () => {
    if (!selectedFile) return

    setIsProcessing(true)
    setProgress(0)
    setError('')
    setOcrResult('')
    setTextBoxes([])

    try {
      // 创建 Tesseract worker，支持中文和英文
      const worker = await createWorker('chi_sim+eng', 1, {
        logger: m => {
          console.log(m)
          if (m.status === 'recognizing text') {
            setProgress(Math.round(m.progress * 100))
          }
        }
      })

      // 设置 OCR 参数以提高识别准确性
      await worker.setParameters({
        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十百千万亿零壹贰叁肆伍陆柒捌玖拾佰仟萬億',
        tessedit_pageseg_mode: '1', // 自动页面分割
      })

      const { data } = await worker.recognize(selectedFile)

      // 设置识别结果
      setOcrResult(data.text.trim())

      // 处理文字框位置信息
      if (data.words && imageRef.current) {
        const img = imageRef.current
        const imgRect = img.getBoundingClientRect()
        const scaleX = imgRect.width / img.naturalWidth
        const scaleY = imgRect.height / img.naturalHeight

        const boxes = data.words
          .filter(word => word.confidence > 30) // 过滤低置信度的识别结果
          .map((word, index) => ({
            id: index,
            text: word.text,
            left: word.bbox.x0 * scaleX,
            top: word.bbox.y0 * scaleY,
            width: (word.bbox.x1 - word.bbox.x0) * scaleX,
            height: (word.bbox.y1 - word.bbox.y0) * scaleY,
            confidence: word.confidence
          }))

        setTextBoxes(boxes)
      }

      await worker.terminate()
    } catch (err) {
      console.error('OCR Error:', err)
      setError(`文字识别失败: ${err.message || '请重试'}`)
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  // 复制文本到剪贴板
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopySuccess('复制成功！')
      setTimeout(() => setCopySuccess(''), 2000)
    } catch (err) {
      console.error('复制失败:', err)
      try {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        setCopySuccess('复制成功！')
        setTimeout(() => setCopySuccess(''), 2000)
      } catch (fallbackErr) {
        console.error('复制失败:', fallbackErr)
        setCopySuccess('复制失败，请手动选择文字复制')
        setTimeout(() => setCopySuccess(''), 3000)
      }
    }
  }

  // 重置应用状态
  const resetApp = () => {
    setSelectedFile(null)
    setImagePreview(null)
    setOcrResult('')
    setError('')
    setProgress(0)
    setTextBoxes([])
    setIsUploading(false)
    setIsProcessing(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <Layout>
      <Head>
        <title>图片文字识别 - OCR 应用</title>
        <meta name="description" content="上传图片，智能识别其中的文字内容" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className={styles.main}>
        <h1 className={styles.title}>
          图片文字识别
        </h1>

        <p className={styles.description}>
          上传图片，智能识别其中的文字内容
        </p>

        {/* 错误提示 */}
        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {/* 成功提示 */}
        {copySuccess && (
          <div className={styles.success}>
            {copySuccess}
          </div>
        )}

        {/* 文件上传区域 */}
        {!imagePreview && (
          <div
            className={styles.uploadArea}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".jpg,.jpeg,.png,.webp"
              onChange={handleFileInputChange}
              className={styles.fileInput}
            />

            {isUploading ? (
              <div className={styles.uploading}>
                <div className={styles.spinner}></div>
                <p>正在上传...</p>
              </div>
            ) : (
              <div className={styles.uploadContent}>
                <div className={styles.uploadIcon}>📁</div>
                <h3>拖拽图片到此处或点击选择文件</h3>
                <p>支持 JPG、PNG、WebP 格式，最大 10MB</p>
              </div>
            )}
          </div>
        )}

        {/* 图片预览和识别区域 */}
        {imagePreview && (
          <div className={styles.previewSection}>
            <div className={styles.imageContainer}>
              <img
                ref={imageRef}
                src={imagePreview}
                alt="预览图片"
                className={styles.previewImage}
                onLoad={() => {
                  // 图片加载完成后可以进行OCR
                }}
              />

              {/* 文字选择层 */}
              {textBoxes.map((box) => (
                <div
                  key={box.id}
                  className={styles.textBox}
                  style={{
                    left: `${box.left}px`,
                    top: `${box.top}px`,
                    width: `${box.width}px`,
                    height: `${box.height}px`,
                  }}
                  title={box.text}
                  onClick={() => copyToClipboard(box.text)}
                >
                  {box.text}
                </div>
              ))}
            </div>

            <div className={styles.controls}>
              <button
                onClick={performOCR}
                disabled={isProcessing}
                className={styles.ocrButton}
              >
                {isProcessing ? '识别中...' : '开始识别文字'}
              </button>

              <button
                onClick={resetApp}
                className={styles.resetButton}
              >
                重新选择图片
              </button>
            </div>

            {/* 进度条 */}
            {isProcessing && (
              <div className={styles.progressContainer}>
                <div className={styles.progressBar}>
                  <div
                    className={styles.progressFill}
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <p>识别进度: {progress}%</p>
              </div>
            )}
          </div>
        )}

        {/* 识别结果 */}
        {ocrResult && (
          <div className={styles.resultSection}>
            <h3>识别结果</h3>
            <div className={styles.resultContainer}>
              <textarea
                value={ocrResult}
                readOnly
                className={styles.resultText}
                rows={10}
              />
              <button
                onClick={() => copyToClipboard(ocrResult)}
                className={styles.copyButton}
              >
                复制全部文字
              </button>
            </div>
          </div>
        )}
      </main>
    </Layout>
  )
}
