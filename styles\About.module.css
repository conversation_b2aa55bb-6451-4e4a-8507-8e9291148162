.main {
  max-width: 800px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

.title {
  font-size: 3rem;
  margin-bottom: 2rem;
  text-align: center;
  color: #333;
}

.content {
  line-height: 1.8;
  color: #555;
}

.content h2 {
  margin: 2rem 0 1rem 0;
  color: #333;
  font-size: 1.8rem;
}

.content p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.content ul {
  margin: 1rem 0;
  padding-left: 2rem;
}

.content li {
  margin-bottom: 0.5rem;
  list-style: none;
  position: relative;
}

.content li::before {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 0.7rem;
  width: 6px;
  height: 6px;
  background: #0070f3;
  border-radius: 50%;
}

.techStack {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
}

.tech {
  background: #f0f8ff;
  color: #0070f3;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid #e0f0ff;
}

@media (max-width: 600px) {
  .main {
    padding: 2rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .content h2 {
    font-size: 1.5rem;
  }
}
